<?php

namespace App\Http\Controllers;

use App\Http\Requests\StoreTechnicalServiceRequest;
use App\Http\Requests\UpdateTechnicalServiceRequest;
use App\Models\TechnicalService;
use App\Models\ServiceCategory;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class TechnicalServiceController extends Controller
{
    /**
     * Display a listing of technical services.
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', TechnicalService::class);

        return view('technical-services.index');
    }

    /**
     * Show the form for creating a new technical service.
     */
    public function create(): View
    {
        $this->authorize('create', TechnicalService::class);

        $categories = ServiceCategory::active()->orderBy('sort_order')->get();

        return view('technical-services.create', compact('categories'));
    }

    /**
     * Store a newly created technical service in storage.
     */
    public function store(StoreTechnicalServiceRequest $request): RedirectResponse
    {
        $this->authorize('create', TechnicalService::class);

        $validated = $request->validated();
        $validated['created_by'] = Auth::id();

        $service = TechnicalService::create($validated);

        return redirect()
            ->route('technical-services.show', $service)
            ->with('success', __('تم إنشاء الخدمة التقنية بنجاح'));
    }

    /**
     * Display the specified technical service.
     */
    public function show(TechnicalService $technicalService): View
    {
        $this->authorize('view', $technicalService);

        $technicalService->load([
            'category',
            'pricingTiers' => function ($query) {
                $query->where('is_active', true)->orderBy('sort_order');
            },
            'templates' => function ($query) {
                $query->where('is_active', true);
            },
            'creator',
            'updater'
        ]);

        return view('technical-services.show', compact('technicalService'));
    }

    /**
     * Show the form for editing the specified technical service.
     */
    public function edit(TechnicalService $technicalService): View
    {
        $this->authorize('update', $technicalService);

        $categories = ServiceCategory::active()->orderBy('sort_order')->get();

        return view('technical-services.edit', compact('technicalService', 'categories'));
    }

    /**
     * Update the specified technical service in storage.
     */
    public function update(UpdateTechnicalServiceRequest $request, TechnicalService $technicalService): RedirectResponse
    {
        $this->authorize('update', $technicalService);

        $validated = $request->validated();
        $validated['updated_by'] = Auth::id();

        $technicalService->update($validated);

        return redirect()
            ->route('technical-services.show', $technicalService)
            ->with('success', __('تم تحديث بيانات الخدمة التقنية بنجاح'));
    }

    /**
     * Remove the specified technical service from storage.
     */
    public function destroy(TechnicalService $technicalService): RedirectResponse
    {
        $this->authorize('delete', $technicalService);

        $serviceName = $technicalService->name_ar ?? $technicalService->name_en;
        $technicalService->delete();

        return redirect()
            ->route('technical-services.index')
            ->with('success', __('تم حذف الخدمة التقنية :name بنجاح', ['name' => $serviceName]));
    }

    /**
     * Toggle the featured status of a technical service.
     */
    public function toggleFeatured(TechnicalService $technicalService): RedirectResponse
    {
        $this->authorize('update', $technicalService);

        $technicalService->update([
            'is_featured' => !$technicalService->is_featured,
            'updated_by' => Auth::id()
        ]);

        $status = $technicalService->is_featured ? 'مميزة' : 'غير مميزة';

        return back()->with('success', __('تم تحديث حالة الخدمة إلى :status', ['status' => $status]));
    }

    /**
     * Update the status of a technical service.
     */
    public function updateStatus(Request $request, TechnicalService $technicalService): RedirectResponse
    {
        $this->authorize('update', $technicalService);

        $request->validate([
            'status' => 'required|in:active,inactive,draft,archived'
        ]);

        $technicalService->update([
            'status' => $request->status,
            'updated_by' => Auth::id()
        ]);

        return back()->with('success', __('تم تحديث حالة الخدمة بنجاح'));
    }

    /**
     * Get technical services data for API/AJAX requests.
     */
    public function getData(Request $request)
    {
        $this->authorize('viewAny', TechnicalService::class);

        $query = TechnicalService::with(['category', 'creator'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('name_ar', 'like', "%{$search}%")
                      ->orWhere('name_en', 'like', "%{$search}%")
                      ->orWhere('description_ar', 'like', "%{$search}%")
                      ->orWhere('description_en', 'like', "%{$search}%");
                });
            })
            ->when($request->category_id, function ($query, $categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->complexity_level, function ($query, $complexity) {
                $query->where('complexity_level', $complexity);
            })
            ->when($request->is_featured !== null, function ($query) use ($request) {
                $query->where('is_featured', $request->boolean('is_featured'));
            });

        // Apply sorting
        $sortField = $request->get('sort', 'created_at');
        $sortDirection = $request->get('direction', 'desc');

        $allowedSortFields = [
            'name_ar', 'name_en', 'base_price', 'estimated_hours',
            'estimated_days', 'complexity_level', 'status', 'is_featured', 'created_at'
        ];

        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection);
        }

        $services = $query->paginate($request->get('per_page', 15));

        return response()->json($services);
    }
}
