<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class UpdatePaymentRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled in the controller
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'client_id' => 'required|exists:clients,id',
            'invoice_id' => 'nullable|exists:invoices,id',
            'amount' => 'required|numeric|min:0.01',
            'currency' => 'required|string|in:SAR,USD,EUR',
            'payment_date' => 'required|date',
            'payment_method' => 'required|in:cash,bank_transfer,credit_card,debit_card,check,online_payment,mobile_payment',
            'reference_number' => 'nullable|string|max:255',
            'transaction_id' => 'nullable|string|max:255',
            'gateway' => 'nullable|string|max:255',
            'gateway_response' => 'nullable|array',
            'status' => 'required|in:pending,completed,failed,cancelled,refunded',
            'notes_ar' => 'nullable|string',
            'notes_en' => 'nullable|string',
            'attachments' => 'nullable|array',
            'attachments.*' => 'file|mimes:pdf,jpg,jpeg,png,gif,doc,docx|max:5120', // 5MB max
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'client_id' => 'العميل',
            'invoice_id' => 'الفاتورة',
            'amount' => 'المبلغ',
            'currency' => 'العملة',
            'payment_date' => 'تاريخ الدفع',
            'payment_method' => 'طريقة الدفع',
            'reference_number' => 'رقم المرجع',
            'transaction_id' => 'رقم المعاملة',
            'gateway' => 'بوابة الدفع',
            'status' => 'حالة الدفعة',
            'notes_ar' => 'ملاحظات بالعربية',
            'notes_en' => 'ملاحظات بالإنجليزية',
            'attachments' => 'المرفقات',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'client_id.required' => 'العميل مطلوب',
            'client_id.exists' => 'العميل المحدد غير صحيح',
            'invoice_id.exists' => 'الفاتورة المحددة غير صحيحة',
            'amount.required' => 'المبلغ مطلوب',
            'amount.numeric' => 'المبلغ يجب أن يكون رقماً',
            'amount.min' => 'المبلغ يجب أن يكون أكبر من صفر',
            'currency.required' => 'العملة مطلوبة',
            'currency.in' => 'العملة المحددة غير صحيحة',
            'payment_date.required' => 'تاريخ الدفع مطلوب',
            'payment_date.date' => 'تاريخ الدفع غير صحيح',
            'payment_method.required' => 'طريقة الدفع مطلوبة',
            'payment_method.in' => 'طريقة الدفع المحددة غير صحيحة',
            'status.required' => 'حالة الدفعة مطلوبة',
            'status.in' => 'حالة الدفعة المحددة غير صحيحة',
            'attachments.*.file' => 'جميع المرفقات يجب أن تكون ملفات صحيحة',
            'attachments.*.mimes' => 'صيغة المرفقات يجب أن تكون: pdf, jpg, jpeg, png, gif, doc, docx',
            'attachments.*.max' => 'حجم كل مرفق يجب أن يكون أقل من 5 ميجابايت',
        ];
    }
}
