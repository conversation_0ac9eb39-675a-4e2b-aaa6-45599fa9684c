<!DOCTYPE html>
<html lang="<?php echo e(str_replace('_', '-', app()->getLocale())); ?>" dir="<?php echo e(app()->getLocale() === 'ar' ? 'rtl' : 'ltr'); ?>" class="rtl">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <meta name="csrf-token" content="<?php echo e(csrf_token()); ?>">

        <title><?php echo e(config('app.name', 'Lial ERP')); ?> - <?php echo e($title ?? __('app.dashboard.title')); ?></title>
        <meta name="description" content="<?php echo e(__('app.company.description')); ?>">

        <!-- Favicon -->
        <link rel="icon" type="image/png" href="<?php echo e(asset('images/logo.png')); ?>">
        <link rel="apple-touch-icon" href="<?php echo e(asset('images/logo.png')); ?>">

        <!-- Arabic Fonts -->
        <link rel="preconnect" href="https://fonts.googleapis.com">
        <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
        <link href="https://fonts.googleapis.com/css2?family=IBM+Plex+Sans+Arabic:wght@300;400;500;600;700&display=swap" rel="stylesheet">

        <!-- Scripts -->
        <?php echo app('Illuminate\Foundation\Vite')(['resources/css/app.css', 'resources/js/app.js']); ?>

        <!-- Additional Styles -->
        <style>
            [x-cloak] { display: none !important; }

            /* Print Styles */
            @media print {
                .no-print {
                    display: none !important;
                }

                .print-only {
                    display: block !important;
                }

                .print-header {
                    position: fixed;
                    top: 0;
                    left: 0;
                    right: 0;
                    height: 2cm;
                    background: white;
                    border-bottom: 1px solid #ccc;
                    padding: 10px;
                    z-index: 1000;
                }

                .print-content {
                    margin-top: 2.5cm;
                }

                .sidebar {
                    display: none !important;
                }

                .main-content {
                    margin-left: 0 !important;
                    width: 100% !important;
                }

                /* Hide navigation and interactive elements */
                .fixed, .sticky, nav, .top-navigation, .sidebar {
                    display: none !important;
                }

                /* Ensure main content takes full width */
                .lg\\:mr-64 {
                    margin-right: 0 !important;
                }
            }
        </style>
    </head>
    <body class="font-arabic antialiased bg-secondary-50" x-data="{ sidebarOpen: false }">
        <div class="min-h-screen lg:flex">
            <!-- Sidebar -->
            <div class="no-print fixed inset-y-0 right-0 z-50 w-64 bg-white shadow-xl transform transition-transform duration-300 ease-in-out lg:translate-x-0 lg:relative lg:flex lg:flex-shrink-0"
                 :class="{ 'translate-x-0': sidebarOpen, 'translate-x-full': !sidebarOpen }"
                 x-cloak>
                <?php if (isset($component)) { $__componentOriginal2880b66d47486b4bfeaf519598a469d6 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal2880b66d47486b4bfeaf519598a469d6 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.sidebar','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('sidebar'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $attributes = $__attributesOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__attributesOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal2880b66d47486b4bfeaf519598a469d6)): ?>
<?php $component = $__componentOriginal2880b66d47486b4bfeaf519598a469d6; ?>
<?php unset($__componentOriginal2880b66d47486b4bfeaf519598a469d6); ?>
<?php endif; ?>
            </div>

            <!-- Mobile sidebar overlay -->
            <div class="no-print fixed inset-0 z-40 bg-secondary-600 bg-opacity-75 transition-opacity duration-300 ease-linear lg:hidden"
                 x-show="sidebarOpen"
                 x-transition:enter="transition-opacity ease-linear duration-300"
                 x-transition:enter-start="opacity-0"
                 x-transition:enter-end="opacity-100"
                 x-transition:leave="transition-opacity ease-linear duration-300"
                 x-transition:leave-start="opacity-100"
                 x-transition:leave-end="opacity-0"
                 @click="sidebarOpen = false"
                 x-cloak>
            </div>

            <!-- Main content -->
            <div class="flex-1 lg:flex lg:flex-col lg:overflow-hidden">
                <!-- Top navigation -->
                <div class="no-print">
                    <?php if (isset($component)) { $__componentOriginal905449b430d1de38c90ac4ac8136b82b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal905449b430d1de38c90ac4ac8136b82b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.top-navigation','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('top-navigation'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal905449b430d1de38c90ac4ac8136b82b)): ?>
<?php $attributes = $__attributesOriginal905449b430d1de38c90ac4ac8136b82b; ?>
<?php unset($__attributesOriginal905449b430d1de38c90ac4ac8136b82b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal905449b430d1de38c90ac4ac8136b82b)): ?>
<?php $component = $__componentOriginal905449b430d1de38c90ac4ac8136b82b; ?>
<?php unset($__componentOriginal905449b430d1de38c90ac4ac8136b82b); ?>
<?php endif; ?>
                </div>

                <!-- Page Heading -->
                <?php if(isset($header)): ?>
                    <header class="bg-white shadow-sm border-b border-secondary-200">
                        <div class="max-w-7xl mx-auto py-4 px-4 sm:px-6 lg:px-8">
                            <div class="flex items-center justify-between">
                                <div>
                                    <?php echo e($header); ?>

                                </div>
                                <div>
                                    <?php if (isset($component)) { $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.breadcrumb','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('breadcrumb'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $attributes = $__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__attributesOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2)): ?>
<?php $component = $__componentOriginale19f62b34dfe0bfdf95075badcb45bc2; ?>
<?php unset($__componentOriginale19f62b34dfe0bfdf95075badcb45bc2); ?>
<?php endif; ?>
                                </div>
                            </div>
                        </div>
                    </header>
                <?php endif; ?>

                <!-- Page Content -->
                <main class="flex-1 overflow-y-auto">
                    <div class="py-6">
                        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
                            <!-- Notifications -->
                            <?php if (isset($component)) { $__componentOriginale5bc9b34dd139a393f71cdc403b71855 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginale5bc9b34dd139a393f71cdc403b71855 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.notifications','data' => []] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('notifications'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes([]); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginale5bc9b34dd139a393f71cdc403b71855)): ?>
<?php $attributes = $__attributesOriginale5bc9b34dd139a393f71cdc403b71855; ?>
<?php unset($__attributesOriginale5bc9b34dd139a393f71cdc403b71855); ?>
<?php endif; ?>
<?php if (isset($__componentOriginale5bc9b34dd139a393f71cdc403b71855)): ?>
<?php $component = $__componentOriginale5bc9b34dd139a393f71cdc403b71855; ?>
<?php unset($__componentOriginale5bc9b34dd139a393f71cdc403b71855); ?>
<?php endif; ?>

                            <!-- Main Content -->
                            <?php echo e($slot); ?>

                        </div>
                    </div>
                </main>
            </div>
        </div>

        <!-- Scripts -->
        <script>
            // Global JavaScript for the application
            document.addEventListener('DOMContentLoaded', function() {
                // Auto-hide notifications after 5 seconds
                setTimeout(function() {
                    const notifications = document.querySelectorAll('[data-auto-hide]');
                    notifications.forEach(function(notification) {
                        notification.style.transition = 'opacity 0.5s ease-out';
                        notification.style.opacity = '0';
                        setTimeout(function() {
                            notification.remove();
                        }, 500);
                    });
                }, 5000);
            });
        </script>
    </body>
</html>
<?php /**PATH C:\laragon\www\lial_Erp\resources\views/layouts/app.blade.php ENDPATH**/ ?>