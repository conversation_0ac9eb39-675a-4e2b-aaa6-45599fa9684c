<?php

namespace App\Policies;

use App\Models\TechnicalService;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class TechnicalServicePolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, TechnicalService $technicalService): bool
    {
        // All authenticated users can view active technical services
        if ($technicalService->status === 'active') {
            return true;
        }

        // Founders, admins, and managers can view all technical services
        if ($user->hasAnyRole(['founder', 'admin', 'manager'])) {
            return true;
        }

        // Employees can view services they created or are assigned to
        if ($user->hasRole('employee')) {
            return $technicalService->created_by === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, TechnicalService $technicalService): bool
    {
        // Founders and admins can update any technical service
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can update services in their scope
        if ($user->hasRole('manager')) {
            return true; // For now, allow all managers to update services
        }

        // Employees can update services they created (if in draft status)
        if ($user->hasRole('employee')) {
            return $technicalService->created_by === $user->id && 
                   in_array($technicalService->status, ['draft', 'inactive']);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, TechnicalService $technicalService): bool
    {
        // Only founders and admins can delete technical services
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can delete draft services
        if ($user->hasRole('manager') && $technicalService->status === 'draft') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, TechnicalService $technicalService): bool
    {
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, TechnicalService $technicalService): bool
    {
        return $user->hasRole('founder');
    }

    /**
     * Determine whether the user can manage pricing tiers.
     */
    public function managePricingTiers(User $user, TechnicalService $technicalService): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can manage service templates.
     */
    public function manageTemplates(User $user, TechnicalService $technicalService): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can feature/unfeature services.
     */
    public function manageFeatures(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can change service status.
     */
    public function changeStatus(User $user, TechnicalService $technicalService): bool
    {
        // Founders and admins can change any status
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can change status for most services
        if ($user->hasRole('manager')) {
            return true;
        }

        // Employees can only change their own services from draft to inactive
        if ($user->hasRole('employee')) {
            return $technicalService->created_by === $user->id && 
                   $technicalService->status === 'draft';
        }

        return false;
    }

    /**
     * Determine whether the user can export technical services data.
     */
    public function export(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can view service analytics.
     */
    public function viewAnalytics(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }
}
