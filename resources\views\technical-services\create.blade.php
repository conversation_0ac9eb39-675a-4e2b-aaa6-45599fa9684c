<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4 space-x-reverse">
                <a href="{{ route('technical-services.index') }}" 
                   class="text-secondary-600 hover:text-secondary-900">
                    <x-icon name="arrow-right" class="w-5 h-5" />
                </a>
                <h2 class="font-semibold text-xl text-secondary-800 leading-tight">
                    إضافة خدمة تقنية جديدة
                </h2>
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-4xl mx-auto px-4 sm:px-6 lg:px-8">
            <form action="{{ route('technical-services.store') }}" method="POST" enctype="multipart/form-data" class="space-y-6">
                @csrf

                <!-- Basic Information -->
                <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                    <div class="px-6 py-4 border-b border-secondary-200">
                        <h3 class="text-lg font-medium text-secondary-900">المعلومات الأساسية</h3>
                    </div>
                    <div class="p-6 space-y-6">
                        <!-- Service Name -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <x-input-label for="name_ar" value="اسم الخدمة (عربي) *" />
                                <x-text-input id="name_ar" name="name_ar" type="text" class="mt-1 block w-full" 
                                              :value="old('name_ar')" required />
                                <x-input-error :messages="$errors->get('name_ar')" class="mt-2" />
                            </div>
                            <div>
                                <x-input-label for="name_en" value="اسم الخدمة (إنجليزي)" />
                                <x-text-input id="name_en" name="name_en" type="text" class="mt-1 block w-full" 
                                              :value="old('name_en')" />
                                <x-input-error :messages="$errors->get('name_en')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Category and Status -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <x-input-label for="category_id" value="فئة الخدمة *" />
                                <select id="category_id" name="category_id" 
                                        class="mt-1 block w-full border-secondary-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm" required>
                                    <option value="">اختر الفئة</option>
                                    @foreach($categories as $category)
                                        <option value="{{ $category->id }}" {{ old('category_id') == $category->id ? 'selected' : '' }}>
                                            {{ $category->name_ar }}
                                        </option>
                                    @endforeach
                                </select>
                                <x-input-error :messages="$errors->get('category_id')" class="mt-2" />
                            </div>
                            <div>
                                <x-input-label for="status" value="حالة الخدمة *" />
                                <select id="status" name="status" 
                                        class="mt-1 block w-full border-secondary-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm" required>
                                    <option value="draft" {{ old('status') == 'draft' ? 'selected' : '' }}>مسودة</option>
                                    <option value="active" {{ old('status') == 'active' ? 'selected' : '' }}>نشط</option>
                                    <option value="inactive" {{ old('status') == 'inactive' ? 'selected' : '' }}>غير نشط</option>
                                </select>
                                <x-input-error :messages="$errors->get('status')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Description -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <x-input-label for="description_ar" value="وصف الخدمة (عربي) *" />
                                <textarea id="description_ar" name="description_ar" rows="4" 
                                          class="mt-1 block w-full border-secondary-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm" 
                                          required>{{ old('description_ar') }}</textarea>
                                <x-input-error :messages="$errors->get('description_ar')" class="mt-2" />
                            </div>
                            <div>
                                <x-input-label for="description_en" value="وصف الخدمة (إنجليزي)" />
                                <textarea id="description_en" name="description_en" rows="4" 
                                          class="mt-1 block w-full border-secondary-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm">{{ old('description_en') }}</textarea>
                                <x-input-error :messages="$errors->get('description_en')" class="mt-2" />
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Pricing and Timeline -->
                <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                    <div class="px-6 py-4 border-b border-secondary-200">
                        <h3 class="text-lg font-medium text-secondary-900">التسعير والجدولة</h3>
                    </div>
                    <div class="p-6 space-y-6">
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div>
                                <x-input-label for="base_price" value="السعر الأساسي (ر.س) *" />
                                <x-text-input id="base_price" name="base_price" type="number" step="0.01" min="0" 
                                              class="mt-1 block w-full" :value="old('base_price')" required />
                                <x-input-error :messages="$errors->get('base_price')" class="mt-2" />
                            </div>
                            <div>
                                <x-input-label for="estimated_hours" value="الساعات المقدرة" />
                                <x-text-input id="estimated_hours" name="estimated_hours" type="number" min="1" 
                                              class="mt-1 block w-full" :value="old('estimated_hours')" />
                                <x-input-error :messages="$errors->get('estimated_hours')" class="mt-2" />
                            </div>
                            <div>
                                <x-input-label for="estimated_days" value="الأيام المقدرة" />
                                <x-text-input id="estimated_days" name="estimated_days" type="number" min="1" 
                                              class="mt-1 block w-full" :value="old('estimated_days')" />
                                <x-input-error :messages="$errors->get('estimated_days')" class="mt-2" />
                            </div>
                        </div>

                        <div>
                            <x-input-label for="complexity_level" value="مستوى التعقيد *" />
                            <select id="complexity_level" name="complexity_level" 
                                    class="mt-1 block w-full border-secondary-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm" required>
                                <option value="">اختر مستوى التعقيد</option>
                                <option value="simple" {{ old('complexity_level') == 'simple' ? 'selected' : '' }}>بسيط</option>
                                <option value="medium" {{ old('complexity_level') == 'medium' ? 'selected' : '' }}>متوسط</option>
                                <option value="complex" {{ old('complexity_level') == 'complex' ? 'selected' : '' }}>معقد</option>
                                <option value="enterprise" {{ old('complexity_level') == 'enterprise' ? 'selected' : '' }}>مؤسسي</option>
                            </select>
                            <x-input-error :messages="$errors->get('complexity_level')" class="mt-2" />
                        </div>
                    </div>
                </div>

                <!-- Features and Options -->
                <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                    <div class="px-6 py-4 border-b border-secondary-200">
                        <h3 class="text-lg font-medium text-secondary-900">المميزات والخيارات</h3>
                    </div>
                    <div class="p-6 space-y-6">
                        <!-- Features -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div>
                                <x-input-label for="features_ar" value="مميزات الخدمة (عربي)" />
                                <textarea id="features_ar" name="features_ar" rows="4" 
                                          class="mt-1 block w-full border-secondary-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm" 
                                          placeholder="اكتب كل ميزة في سطر منفصل">{{ old('features_ar') }}</textarea>
                                <x-input-error :messages="$errors->get('features_ar')" class="mt-2" />
                            </div>
                            <div>
                                <x-input-label for="features_en" value="مميزات الخدمة (إنجليزي)" />
                                <textarea id="features_en" name="features_en" rows="4" 
                                          class="mt-1 block w-full border-secondary-300 focus:border-primary-500 focus:ring-primary-500 rounded-md shadow-sm" 
                                          placeholder="Write each feature on a separate line">{{ old('features_en') }}</textarea>
                                <x-input-error :messages="$errors->get('features_en')" class="mt-2" />
                            </div>
                        </div>

                        <!-- Service Options -->
                        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                            <div class="flex items-center">
                                <input id="is_featured" name="is_featured" type="checkbox" value="1" 
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                                       {{ old('is_featured') ? 'checked' : '' }}>
                                <label for="is_featured" class="mr-2 block text-sm text-secondary-900">
                                    خدمة مميزة
                                </label>
                            </div>
                            <div class="flex items-center">
                                <input id="is_customizable" name="is_customizable" type="checkbox" value="1" 
                                       class="h-4 w-4 text-primary-600 focus:ring-primary-500 border-secondary-300 rounded"
                                       {{ old('is_customizable') ? 'checked' : '' }}>
                                <label for="is_customizable" class="mr-2 block text-sm text-secondary-900">
                                    قابلة للتخصيص
                                </label>
                            </div>
                        </div>

                        <!-- Service Image -->
                        <div>
                            <x-input-label for="image" value="صورة الخدمة" />
                            <input id="image" name="image" type="file" accept="image/*" 
                                   class="mt-1 block w-full text-sm text-secondary-500 file:mr-4 file:py-2 file:px-4 file:rounded-full file:border-0 file:text-sm file:font-semibold file:bg-primary-50 file:text-primary-700 hover:file:bg-primary-100" />
                            <x-input-error :messages="$errors->get('image')" class="mt-2" />
                        </div>
                    </div>
                </div>

                <!-- Action Buttons -->
                <div class="flex items-center justify-end space-x-4 space-x-reverse">
                    <a href="{{ route('technical-services.index') }}" 
                       class="bg-secondary-600 hover:bg-secondary-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        إلغاء
                    </a>
                    <button type="submit" 
                            class="bg-primary-600 hover:bg-primary-700 text-white px-6 py-2 rounded-lg text-sm font-medium transition-colors duration-200">
                        حفظ الخدمة
                    </button>
                </div>
            </form>
        </div>
    </div>
</x-app-layout>
