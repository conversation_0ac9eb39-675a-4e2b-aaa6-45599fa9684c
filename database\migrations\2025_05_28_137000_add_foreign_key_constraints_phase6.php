<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Facades\DB;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // Add foreign key constraint for category_id only (other constraints already exist)
        Schema::table('expenses', function (Blueprint $table) {
            // Check if the foreign key doesn't already exist
            $foreignKeys = collect(DB::select("
                SELECT CONSTRAINT_NAME
                FROM information_schema.KEY_COLUMN_USAGE
                WHERE TABLE_SCHEMA = DATABASE()
                AND TABLE_NAME = 'expenses'
                AND CONSTRAINT_NAME = 'expenses_category_id_foreign'
            "))->pluck('CONSTRAINT_NAME')->toArray();

            if (empty($foreignKeys)) {
                $table->foreign('category_id')->references('id')->on('expense_categories')->onDelete('restrict');
            }
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('expenses', function (Blueprint $table) {
            // Only drop the category_id foreign key (others are managed by different migrations)
            $table->dropForeign(['category_id']);
        });
    }
};
