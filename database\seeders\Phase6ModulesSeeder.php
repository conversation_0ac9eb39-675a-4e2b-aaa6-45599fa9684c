<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\Department;
use App\Models\ExpenseCategory;
use App\Models\ProductCategory;
use App\Models\Product;
use App\Models\ServiceCategory;
use App\Models\TechnicalService;
use App\Models\User;

class Phase6ModulesSeeder extends Seeder
{
    /**
     * Run the database seeders.
     */
    public function run(): void
    {
        $this->seedDepartments();
        $this->seedExpenseCategories();
        $this->seedProductCategories();
        $this->seedSampleProducts();
        $this->seedServiceCategories();
        $this->seedTechnicalServices();
    }

    /**
     * Seed departments.
     */
    private function seedDepartments(): void
    {
        $departments = [
            [
                'code' => 'MGMT',
                'name_ar' => 'الإدارة العليا',
                'name_en' => 'Management',
                'description_ar' => 'الإدارة العليا والقيادة التنفيذية',
                'color' => '#1f2937',
                'icon' => 'building-office',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'code' => 'DEV',
                'name_ar' => 'فريق التطوير',
                'name_en' => 'Development Team',
                'description_ar' => 'فريق تطوير البرمجيات والتطبيقات',
                'color' => '#3b82f6',
                'icon' => 'code-bracket',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'DESIGN',
                'name_ar' => 'فريق التصميم',
                'name_en' => 'Design Team',
                'description_ar' => 'فريق التصميم الجرافيكي وتجربة المستخدم',
                'color' => '#8b5cf6',
                'icon' => 'paint-brush',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'code' => 'SALES',
                'name_ar' => 'فريق المبيعات',
                'name_en' => 'Sales Team',
                'description_ar' => 'فريق المبيعات وخدمة العملاء',
                'color' => '#10b981',
                'icon' => 'chart-bar',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'code' => 'MARKETING',
                'name_ar' => 'فريق التسويق',
                'name_en' => 'Marketing Team',
                'description_ar' => 'فريق التسويق الرقمي والإعلانات',
                'color' => '#f59e0b',
                'icon' => 'megaphone',
                'is_active' => true,
                'sort_order' => 5,
            ],
            [
                'code' => 'FINANCE',
                'name_ar' => 'فريق المحاسبة',
                'name_en' => 'Finance Team',
                'description_ar' => 'فريق المحاسبة والشؤون المالية',
                'color' => '#ef4444',
                'icon' => 'calculator',
                'is_active' => true,
                'sort_order' => 6,
            ],
        ];

        foreach ($departments as $department) {
            Department::firstOrCreate(
                ['code' => $department['code']],
                $department
            );
        }
    }

    /**
     * Seed expense categories.
     */
    private function seedExpenseCategories(): void
    {
        $categories = [
            [
                'code' => 'OFFICE',
                'name_ar' => 'مستلزمات المكتب',
                'name_en' => 'Office Supplies',
                'description_ar' => 'مستلزمات وأدوات المكتب',
                'color' => '#6b7280',
                'icon' => 'building-office',
                'requires_approval' => false,
                'approval_limit' => 1000.00,
                'sort_order' => 1,
            ],
            [
                'code' => 'SOFTWARE',
                'name_ar' => 'البرمجيات والتراخيص',
                'name_en' => 'Software & Licenses',
                'description_ar' => 'تراخيص البرمجيات والأدوات التقنية',
                'color' => '#3b82f6',
                'icon' => 'computer-desktop',
                'requires_approval' => true,
                'approval_limit' => 500.00,
                'sort_order' => 2,
            ],
            [
                'code' => 'HARDWARE',
                'name_ar' => 'الأجهزة والمعدات',
                'name_en' => 'Hardware & Equipment',
                'description_ar' => 'أجهزة الكمبيوتر والمعدات التقنية',
                'color' => '#8b5cf6',
                'icon' => 'cpu-chip',
                'requires_approval' => true,
                'approval_limit' => 2000.00,
                'sort_order' => 3,
            ],
            [
                'code' => 'MARKETING',
                'name_ar' => 'التسويق والإعلان',
                'name_en' => 'Marketing & Advertising',
                'description_ar' => 'مصاريف التسويق والحملات الإعلانية',
                'color' => '#f59e0b',
                'icon' => 'megaphone',
                'requires_approval' => true,
                'approval_limit' => 5000.00,
                'sort_order' => 4,
            ],
            [
                'code' => 'TRAVEL',
                'name_ar' => 'السفر والانتقال',
                'name_en' => 'Travel & Transportation',
                'description_ar' => 'مصاريف السفر والمواصلات',
                'color' => '#10b981',
                'icon' => 'truck',
                'requires_approval' => true,
                'approval_limit' => 3000.00,
                'sort_order' => 5,
            ],
            [
                'code' => 'UTILITIES',
                'name_ar' => 'المرافق والخدمات',
                'name_en' => 'Utilities & Services',
                'description_ar' => 'فواتير الكهرباء والماء والإنترنت',
                'color' => '#ef4444',
                'icon' => 'bolt',
                'requires_approval' => false,
                'approval_limit' => 2000.00,
                'sort_order' => 6,
            ],
            [
                'code' => 'TRAINING',
                'name_ar' => 'التدريب والتطوير',
                'name_en' => 'Training & Development',
                'description_ar' => 'دورات التدريب وتطوير المهارات',
                'color' => '#06b6d4',
                'icon' => 'academic-cap',
                'requires_approval' => true,
                'approval_limit' => 2000.00,
                'sort_order' => 7,
            ],
        ];

        foreach ($categories as $category) {
            ExpenseCategory::firstOrCreate(
                ['code' => $category['code']],
                $category
            );
        }
    }

    /**
     * Seed product categories.
     */
    private function seedProductCategories(): void
    {
        $categories = [
            [
                'code' => 'SERVICES',
                'name_ar' => 'الخدمات التقنية',
                'name_en' => 'Technical Services',
                'description_ar' => 'خدمات التطوير والتصميم',
                'color' => '#3b82f6',
                'icon' => 'cog',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'code' => 'PRODUCTS',
                'name_ar' => 'المنتجات الرقمية',
                'name_en' => 'Digital Products',
                'description_ar' => 'المنتجات والحلول الرقمية',
                'color' => '#8b5cf6',
                'icon' => 'cube',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'code' => 'HOSTING',
                'name_ar' => 'خدمات الاستضافة',
                'name_en' => 'Hosting Services',
                'description_ar' => 'خدمات الاستضافة والنطاقات',
                'color' => '#10b981',
                'icon' => 'server',
                'is_active' => true,
                'sort_order' => 3,
            ],
        ];

        foreach ($categories as $category) {
            ProductCategory::firstOrCreate(
                ['code' => $category['code']],
                $category
            );
        }
    }

    /**
     * Seed sample products.
     */
    private function seedSampleProducts(): void
    {
        $servicesCategory = ProductCategory::where('code', 'SERVICES')->first();
        $productsCategory = ProductCategory::where('code', 'PRODUCTS')->first();
        $hostingCategory = ProductCategory::where('code', 'HOSTING')->first();

        if (!$servicesCategory || !$productsCategory || !$hostingCategory) {
            return;
        }

        $products = [
            [
                'category_id' => $servicesCategory->id,
                'name_ar' => 'تطوير موقع إلكتروني',
                'name_en' => 'Website Development',
                'description_ar' => 'تطوير موقع إلكتروني متكامل',
                'type' => 'service',
                'cost_price' => 5000.00,
                'selling_price' => 8000.00,
                'track_inventory' => false,
                'unit_ar' => 'مشروع',
                'unit_en' => 'Project',
                'created_by' => 1,
            ],
            [
                'category_id' => $servicesCategory->id,
                'name_ar' => 'تطوير تطبيق جوال',
                'name_en' => 'Mobile App Development',
                'description_ar' => 'تطوير تطبيق جوال للأندرويد والآيفون',
                'type' => 'service',
                'cost_price' => 15000.00,
                'selling_price' => 25000.00,
                'track_inventory' => false,
                'unit_ar' => 'مشروع',
                'unit_en' => 'Project',
                'created_by' => 1,
            ],
            [
                'category_id' => $hostingCategory->id,
                'name_ar' => 'استضافة مشتركة',
                'name_en' => 'Shared Hosting',
                'description_ar' => 'استضافة مشتركة سنوية',
                'type' => 'service',
                'cost_price' => 200.00,
                'selling_price' => 400.00,
                'track_inventory' => true,
                'current_stock' => 100,
                'min_stock_level' => 10,
                'unit_ar' => 'سنة',
                'unit_en' => 'Year',
                'created_by' => 1,
            ],
        ];

        foreach ($products as $product) {
            Product::firstOrCreate(
                [
                    'name_ar' => $product['name_ar'],
                    'category_id' => $product['category_id']
                ],
                $product
            );
        }
    }

    /**
     * Seed service categories.
     */
    private function seedServiceCategories(): void
    {
        $categories = [
            [
                'name_ar' => 'تطوير المواقع الإلكترونية',
                'name_en' => 'Web Development',
                'description_ar' => 'خدمات تطوير المواقع الإلكترونية والتطبيقات الويب',
                'description_en' => 'Website and web application development services',
                'icon' => 'globe-alt',
                'color' => '#3b82f6',
                'is_active' => true,
                'sort_order' => 1,
            ],
            [
                'name_ar' => 'تطوير التطبيقات الجوالة',
                'name_en' => 'Mobile App Development',
                'description_ar' => 'خدمات تطوير التطبيقات للأندرويد والآيفون',
                'description_en' => 'Android and iOS mobile application development',
                'icon' => 'device-phone-mobile',
                'color' => '#10b981',
                'is_active' => true,
                'sort_order' => 2,
            ],
            [
                'name_ar' => 'التصميم الجرافيكي',
                'name_en' => 'Graphic Design',
                'description_ar' => 'خدمات التصميم الجرافيكي والهوية البصرية',
                'description_en' => 'Graphic design and visual identity services',
                'icon' => 'paint-brush',
                'color' => '#8b5cf6',
                'is_active' => true,
                'sort_order' => 3,
            ],
            [
                'name_ar' => 'التسويق الرقمي',
                'name_en' => 'Digital Marketing',
                'description_ar' => 'خدمات التسويق الرقمي ووسائل التواصل الاجتماعي',
                'description_en' => 'Digital marketing and social media services',
                'icon' => 'megaphone',
                'color' => '#f59e0b',
                'is_active' => true,
                'sort_order' => 4,
            ],
            [
                'name_ar' => 'الاستشارات التقنية',
                'name_en' => 'Technical Consulting',
                'description_ar' => 'خدمات الاستشارات التقنية والحلول المتخصصة',
                'description_en' => 'Technical consulting and specialized solutions',
                'icon' => 'light-bulb',
                'color' => '#ef4444',
                'is_active' => true,
                'sort_order' => 5,
            ],
        ];

        foreach ($categories as $category) {
            ServiceCategory::firstOrCreate(
                ['name_ar' => $category['name_ar']],
                $category
            );
        }
    }

    /**
     * Seed technical services.
     */
    private function seedTechnicalServices(): void
    {
        $webDevCategory = ServiceCategory::where('name_ar', 'تطوير المواقع الإلكترونية')->first();
        $mobileDevCategory = ServiceCategory::where('name_ar', 'تطوير التطبيقات الجوالة')->first();
        $designCategory = ServiceCategory::where('name_ar', 'التصميم الجرافيكي')->first();

        if (!$webDevCategory || !$mobileDevCategory || !$designCategory) {
            return;
        }

        $services = [
            [
                'name_ar' => 'موقع إلكتروني تعريفي',
                'name_en' => 'Corporate Website',
                'description_ar' => 'تطوير موقع إلكتروني تعريفي للشركات والمؤسسات مع لوحة تحكم إدارية',
                'description_en' => 'Corporate website development with admin panel',
                'features_ar' => "تصميم متجاوب مع جميع الأجهزة\nلوحة تحكم إدارية\nتحسين محركات البحث\nنظام إدارة المحتوى\nنماذج التواصل",
                'features_en' => "Responsive design\nAdmin panel\nSEO optimization\nContent management system\nContact forms",
                'category_id' => $webDevCategory->id,
                'base_price' => 5000.00,
                'estimated_hours' => 120,
                'estimated_days' => 15,
                'complexity_level' => 'medium',
                'status' => 'active',
                'requirements' => ['محتوى الموقع', 'الشعار والهوية البصرية', 'صور عالية الجودة'],
                'deliverables' => ['الموقع الإلكتروني', 'لوحة التحكم', 'دليل الاستخدام', 'سنة استضافة مجانية'],
                'technologies' => ['Laravel', 'Vue.js', 'MySQL', 'Tailwind CSS'],
                'is_featured' => true,
                'is_customizable' => true,
                'created_by' => 1,
            ],
            [
                'name_ar' => 'متجر إلكتروني',
                'name_en' => 'E-commerce Store',
                'description_ar' => 'تطوير متجر إلكتروني متكامل مع نظام إدارة المنتجات والطلبات والمدفوعات',
                'description_en' => 'Complete e-commerce store with product, order and payment management',
                'features_ar' => "إدارة المنتجات والفئات\nسلة التسوق\nبوابات الدفع المتعددة\nإدارة الطلبات\nنظام الخصومات والكوبونات\nتقارير المبيعات",
                'category_id' => $webDevCategory->id,
                'base_price' => 15000.00,
                'estimated_hours' => 300,
                'estimated_days' => 45,
                'complexity_level' => 'complex',
                'status' => 'active',
                'requirements' => ['قائمة المنتجات', 'بيانات الشركة', 'حسابات بوابات الدفع'],
                'deliverables' => ['المتجر الإلكتروني', 'لوحة التحكم', 'تطبيق الجوال', 'التدريب'],
                'technologies' => ['Laravel', 'Vue.js', 'MySQL', 'Payment Gateways'],
                'is_featured' => true,
                'is_customizable' => true,
                'created_by' => 1,
            ],
            [
                'name_ar' => 'تطبيق جوال أندرويد',
                'name_en' => 'Android Mobile App',
                'description_ar' => 'تطوير تطبيق جوال للأندرويد مع واجهة مستخدم حديثة وأداء عالي',
                'description_en' => 'Android mobile app with modern UI and high performance',
                'features_ar' => "واجهة مستخدم حديثة\nدعم اللغة العربية\nإشعارات فورية\nتزامن البيانات\nدعم الوضع المظلم",
                'category_id' => $mobileDevCategory->id,
                'base_price' => 12000.00,
                'estimated_hours' => 250,
                'estimated_days' => 35,
                'complexity_level' => 'complex',
                'status' => 'active',
                'requirements' => ['متطلبات التطبيق', 'التصميم المطلوب', 'API الخلفي'],
                'deliverables' => ['التطبيق', 'الكود المصدري', 'دليل النشر', 'الدعم الفني'],
                'technologies' => ['Flutter', 'Dart', 'Firebase', 'REST API'],
                'is_featured' => false,
                'is_customizable' => true,
                'created_by' => 1,
            ],
            [
                'name_ar' => 'تصميم هوية بصرية',
                'name_en' => 'Brand Identity Design',
                'description_ar' => 'تصميم هوية بصرية متكاملة تشمل الشعار والألوان والخطوط',
                'description_en' => 'Complete brand identity including logo, colors and fonts',
                'features_ar' => "تصميم الشعار\nدليل الهوية البصرية\nبطاقات العمل\nورق الرسائل\nالأختام",
                'category_id' => $designCategory->id,
                'base_price' => 3000.00,
                'estimated_hours' => 80,
                'estimated_days' => 10,
                'complexity_level' => 'medium',
                'status' => 'active',
                'requirements' => ['اسم الشركة', 'طبيعة النشاط', 'الألوان المفضلة'],
                'deliverables' => ['ملفات الشعار', 'دليل الهوية', 'التصاميم الطباعية'],
                'technologies' => ['Adobe Illustrator', 'Adobe Photoshop', 'Figma'],
                'is_featured' => false,
                'is_customizable' => true,
                'created_by' => 1,
            ],
        ];

        foreach ($services as $service) {
            TechnicalService::firstOrCreate(
                [
                    'name_ar' => $service['name_ar'],
                    'category_id' => $service['category_id']
                ],
                $service
            );
        }
    }
}
