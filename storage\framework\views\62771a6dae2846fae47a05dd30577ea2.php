
<div class="flex flex-col h-full">
    
    <div class="flex items-center justify-center px-6 py-6 border-b border-secondary-200 bg-gradient-to-r from-primary-50 to-primary-100">
        <?php if (isset($component)) { $__componentOriginal8892e718f3d0d7a916180885c6f012e7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal8892e718f3d0d7a916180885c6f012e7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.application-logo','data' => ['size' => 'default','variant' => 'compact','clickable' => true,'showText' => true,'class' => 'w-full']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('application-logo'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['size' => 'default','variant' => 'compact','clickable' => true,'showText' => true,'class' => 'w-full']); ?>
<?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $attributes = $__attributesOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__attributesOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal8892e718f3d0d7a916180885c6f012e7)): ?>
<?php $component = $__componentOriginal8892e718f3d0d7a916180885c6f012e7; ?>
<?php unset($__componentOriginal8892e718f3d0d7a916180885c6f012e7); ?>
<?php endif; ?>
    </div>

    
    <div class="px-6 py-4 border-b border-secondary-200 bg-secondary-50">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                    <span class="text-sm font-medium text-white">
                        <?php echo e(substr(auth()->user()->name, 0, 2)); ?>

                    </span>
                </div>
            </div>
            <div class="mr-3 text-right">
                <div class="text-sm font-medium text-secondary-900"><?php echo e(auth()->user()->name); ?></div>
                <div class="text-xs text-secondary-500">
                    <?php if(auth()->user()->hasRole('founder')): ?>
                        <?php echo e(__('app.roles.founder')); ?>

                    <?php elseif(auth()->user()->hasRole('admin')): ?>
                        <?php echo e(__('app.roles.admin')); ?>

                    <?php elseif(auth()->user()->hasRole('manager')): ?>
                        <?php echo e(__('app.roles.manager')); ?>

                    <?php elseif(auth()->user()->hasRole('employee')): ?>
                        <?php echo e(__('app.roles.employee')); ?>

                    <?php elseif(auth()->user()->hasRole('client')): ?>
                        <?php echo e(__('app.roles.client')); ?>

                    <?php endif; ?>
                </div>
            </div>
        </div>
    </div>

    
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        
        <?php if (isset($component)) { $__componentOriginal6cced52613a484e7295a90162a92d81b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6cced52613a484e7295a90162a92d81b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-item','data' => ['href' => ''.e(route('dashboard')).'','active' => request()->routeIs('dashboard'),'icon' => 'home']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('dashboard')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('dashboard')),'icon' => 'home']); ?>
            <?php echo e(__('app.navigation.dashboard')); ?>

         <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $attributes = $__attributesOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__attributesOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $component = $__componentOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__componentOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('viewAny', App\Models\TechnicalService::class)): ?>
            <?php if (isset($component)) { $__componentOriginal6cced52613a484e7295a90162a92d81b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6cced52613a484e7295a90162a92d81b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-item','data' => ['href' => ''.e(route('technical-services.index')).'','active' => request()->routeIs('technical-services.*'),'icon' => 'cog']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('technical-services.index')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('technical-services.*')),'icon' => 'cog']); ?>
                <?php echo e(__('app.navigation.technical_services')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $attributes = $__attributesOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__attributesOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $component = $__componentOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__componentOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('viewAny', App\Models\Client::class)): ?>
            <?php if (isset($component)) { $__componentOriginal6cced52613a484e7295a90162a92d81b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6cced52613a484e7295a90162a92d81b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-item','data' => ['href' => ''.e(route('clients.index')).'','active' => request()->routeIs('clients.*'),'icon' => 'users']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('clients.index')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('clients.*')),'icon' => 'users']); ?>
                <?php echo e(__('app.navigation.clients')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $attributes = $__attributesOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__attributesOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $component = $__componentOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__componentOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_finance')): ?>
            <?php if (isset($component)) { $__componentOriginal42092016247fb3828aa5e1952af5e0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42092016247fb3828aa5e1952af5e0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-group','data' => ['label' => ''.e(__('app.navigation.finance')).'','icon' => 'currency-dollar','active' => request()->routeIs('finance.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e(__('app.navigation.finance')).'','icon' => 'currency-dollar','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('finance.*'))]); ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('invoices.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('invoices.*'))]); ?>
                    <?php echo e(__('app.finance.invoices')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('payments.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('payments.*'))]); ?>
                    <?php echo e(__('app.finance.payments')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('expenses.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('expenses.*'))]); ?>
                    <?php echo e(__('app.finance.expenses')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('reports.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.*'))]); ?>
                    <?php echo e(__('app.finance.reports')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $attributes = $__attributesOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $component = $__componentOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__componentOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('viewAny', App\Models\Project::class)): ?>
            <?php if (isset($component)) { $__componentOriginal42092016247fb3828aa5e1952af5e0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42092016247fb3828aa5e1952af5e0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-group','data' => ['label' => ''.e(__('app.navigation.projects')).'','icon' => 'clipboard-list','active' => request()->routeIs('projects.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e(__('app.navigation.projects')).'','icon' => 'clipboard-list','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('projects.*'))]); ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('projects.index')).'','active' => request()->routeIs('projects.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('projects.index')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('projects.index'))]); ?>
                    جميع المشاريع
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('projects.tasks.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('projects.tasks.*'))]); ?>
                    <?php echo e(__('app.projects.tasks')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('projects.files.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('projects.files.*'))]); ?>
                    <?php echo e(__('app.projects.files')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('projects.milestones.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('projects.milestones.*'))]); ?>
                    <?php echo e(__('app.projects.milestones')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $attributes = $__attributesOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $component = $__componentOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__componentOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('viewAny', App\Models\Invoice::class)): ?>
            <?php if (isset($component)) { $__componentOriginal42092016247fb3828aa5e1952af5e0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42092016247fb3828aa5e1952af5e0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-group','data' => ['label' => ''.e(__('app.navigation.finances')).'','icon' => 'banknotes','active' => request()->routeIs('invoices.*', 'payments.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e(__('app.navigation.finances')).'','icon' => 'banknotes','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('invoices.*', 'payments.*'))]); ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('invoices.index')).'','active' => request()->routeIs('invoices.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('invoices.index')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('invoices.*'))]); ?>
                    <?php echo e(__('app.finances.invoices')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('payments.index')).'','active' => request()->routeIs('payments.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('payments.index')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('payments.*'))]); ?>
                    <?php echo e(__('app.finances.payments')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('expenses.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('expenses.*'))]); ?>
                    <?php echo e(__('app.finances.expenses')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => '#','active' => request()->routeIs('reports.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.*'))]); ?>
                    <?php echo e(__('app.finances.reports')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $attributes = $__attributesOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $component = $__componentOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__componentOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_team')): ?>
            <?php if (isset($component)) { $__componentOriginal42092016247fb3828aa5e1952af5e0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42092016247fb3828aa5e1952af5e0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-group','data' => ['label' => ''.e(__('app.navigation.team')).'','icon' => 'user-group','active' => request()->routeIs('team.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e(__('app.navigation.team')).'','icon' => 'user-group','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('team.*'))]); ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('team.departments')).'','active' => request()->routeIs('team.departments')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('team.departments')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('team.departments'))]); ?>
                    الأقسام
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('team.members')).'','active' => request()->routeIs('team.members')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('team.members')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('team.members'))]); ?>
                    أعضاء الفريق
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('team.attendance')).'','active' => request()->routeIs('team.attendance')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('team.attendance')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('team.attendance'))]); ?>
                    <?php echo e(__('app.team.attendance')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('team.performance')).'','active' => request()->routeIs('team.performance')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('team.performance')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('team.performance'))]); ?>
                    <?php echo e(__('app.team.performance')); ?>

                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $attributes = $__attributesOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $component = $__componentOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__componentOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_inventory')): ?>
            <?php if (isset($component)) { $__componentOriginal42092016247fb3828aa5e1952af5e0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42092016247fb3828aa5e1952af5e0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-group','data' => ['label' => ''.e(__('app.navigation.inventory')).'','icon' => 'cube','active' => request()->routeIs('inventory.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e(__('app.navigation.inventory')).'','icon' => 'cube','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('inventory.*'))]); ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('inventory.products')).'','active' => request()->routeIs('inventory.products')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('inventory.products')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('inventory.products'))]); ?>
                    المنتجات والخدمات
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('inventory.categories')).'','active' => request()->routeIs('inventory.categories')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('inventory.categories')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('inventory.categories'))]); ?>
                    فئات المنتجات
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('inventory.movements')).'','active' => request()->routeIs('inventory.movements')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('inventory.movements')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('inventory.movements'))]); ?>
                    حركات المخزون
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('inventory.alerts')).'','active' => request()->routeIs('inventory.alerts')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('inventory.alerts')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('inventory.alerts'))]); ?>
                    تنبيهات المخزون
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $attributes = $__attributesOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $component = $__componentOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__componentOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_expenses')): ?>
            <?php if (isset($component)) { $__componentOriginal42092016247fb3828aa5e1952af5e0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42092016247fb3828aa5e1952af5e0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-group','data' => ['label' => ''.e(__('app.navigation.expenses')).'','icon' => 'banknotes','active' => request()->routeIs('expenses.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e(__('app.navigation.expenses')).'','icon' => 'banknotes','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('expenses.*'))]); ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('expenses.index')).'','active' => request()->routeIs('expenses.index')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('expenses.index')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('expenses.index'))]); ?>
                    المصروفات
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('expenses.categories')).'','active' => request()->routeIs('expenses.categories')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('expenses.categories')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('expenses.categories'))]); ?>
                    فئات المصروفات
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('expenses.budgets')).'','active' => request()->routeIs('expenses.budgets')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('expenses.budgets')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('expenses.budgets'))]); ?>
                    الميزانيات
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('expenses.reports')).'','active' => request()->routeIs('expenses.reports')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('expenses.reports')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('expenses.reports'))]); ?>
                    تقارير المصروفات
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $attributes = $__attributesOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $component = $__componentOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__componentOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_analytics')): ?>
            <?php if (isset($component)) { $__componentOriginal42092016247fb3828aa5e1952af5e0d7 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal42092016247fb3828aa5e1952af5e0d7 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-group','data' => ['label' => ''.e(__('app.navigation.reports')).'','icon' => 'chart-bar','active' => request()->routeIs('reports.*')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-group'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['label' => ''.e(__('app.navigation.reports')).'','icon' => 'chart-bar','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.*'))]); ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('reports.dashboard')).'','active' => request()->routeIs('reports.dashboard')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('reports.dashboard')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.dashboard'))]); ?>
                    لوحة التقارير
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('reports.financial')).'','active' => request()->routeIs('reports.financial')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('reports.financial')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.financial'))]); ?>
                    التقارير المالية
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('reports.inventory')).'','active' => request()->routeIs('reports.inventory')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('reports.inventory')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.inventory'))]); ?>
                    تقارير المخزون
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('reports.team')).'','active' => request()->routeIs('reports.team')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('reports.team')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.team'))]); ?>
                    تقارير الفريق
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
                <?php if (isset($component)) { $__componentOriginalf8447ce692939503b2fe276b1f7d1c65 = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65 = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-sub-item','data' => ['href' => ''.e(route('reports.analytics')).'','active' => request()->routeIs('reports.analytics')]] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-sub-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => ''.e(route('reports.analytics')).'','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('reports.analytics'))]); ?>
                    التحليلات المتقدمة
                 <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $attributes = $__attributesOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__attributesOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
<?php if (isset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65)): ?>
<?php $component = $__componentOriginalf8447ce692939503b2fe276b1f7d1c65; ?>
<?php unset($__componentOriginalf8447ce692939503b2fe276b1f7d1c65); ?>
<?php endif; ?>
             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $attributes = $__attributesOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__attributesOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal42092016247fb3828aa5e1952af5e0d7)): ?>
<?php $component = $__componentOriginal42092016247fb3828aa5e1952af5e0d7; ?>
<?php unset($__componentOriginal42092016247fb3828aa5e1952af5e0d7); ?>
<?php endif; ?>
        <?php endif; ?>

        
        <?php if (app(\Illuminate\Contracts\Auth\Access\Gate::class)->check('view_system_settings')): ?>
            <?php if (isset($component)) { $__componentOriginal6cced52613a484e7295a90162a92d81b = $component; } ?>
<?php if (isset($attributes)) { $__attributesOriginal6cced52613a484e7295a90162a92d81b = $attributes; } ?>
<?php $component = Illuminate\View\AnonymousComponent::resolve(['view' => 'components.nav-item','data' => ['href' => '#','active' => request()->routeIs('settings.*'),'icon' => 'cog-6-tooth']] + (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag ? $attributes->all() : [])); ?>
<?php $component->withName('nav-item'); ?>
<?php if ($component->shouldRender()): ?>
<?php $__env->startComponent($component->resolveView(), $component->data()); ?>
<?php if (isset($attributes) && $attributes instanceof Illuminate\View\ComponentAttributeBag): ?>
<?php $attributes = $attributes->except(\Illuminate\View\AnonymousComponent::ignoredParameterNames()); ?>
<?php endif; ?>
<?php $component->withAttributes(['href' => '#','active' => \Illuminate\View\Compilers\BladeCompiler::sanitizeComponentAttribute(request()->routeIs('settings.*')),'icon' => 'cog-6-tooth']); ?>
                <?php echo e(__('app.navigation.settings')); ?>

             <?php echo $__env->renderComponent(); ?>
<?php endif; ?>
<?php if (isset($__attributesOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $attributes = $__attributesOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__attributesOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>
<?php if (isset($__componentOriginal6cced52613a484e7295a90162a92d81b)): ?>
<?php $component = $__componentOriginal6cced52613a484e7295a90162a92d81b; ?>
<?php unset($__componentOriginal6cced52613a484e7295a90162a92d81b); ?>
<?php endif; ?>
        <?php endif; ?>
    </nav>

    
    <div class="px-6 py-4 border-t border-secondary-200">
        <div class="text-center">
            <p class="text-xs text-secondary-500">&copy; <?php echo e(date('Y')); ?> <?php echo e(__('app.company.name')); ?></p>
            <p class="text-xs text-secondary-400 mt-1">الإصدار 1.0.0</p>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\lial_Erp\resources\views/components/sidebar.blade.php ENDPATH**/ ?>