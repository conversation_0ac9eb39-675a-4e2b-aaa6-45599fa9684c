
<div class="space-y-6">
    
    <div class="bg-gradient-to-r from-primary-600 to-primary-700 rounded-xl p-6 text-white">
        <div class="flex items-center justify-between">
            <div>
                <h3 class="text-xl font-bold font-arabic">مرحباً بك في نظام إدارة ليال</h3>
                <p class="text-primary-100 mt-1">لوحة تحكم المؤسس - إدارة شاملة للنظام</p>
            </div>
            <div class="text-right">
                <div class="text-2xl font-bold"><?php echo e(date('Y/m/d')); ?></div>
                <div class="text-primary-200 text-sm"><?php echo e(date('l')); ?></div>
            </div>
        </div>
    </div>

    
    <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
        
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">إجمالي العملاء</p>
                    <p class="text-2xl font-bold text-secondary-900"><?php echo e($stats['total_clients'] ?? 0); ?></p>
                    <p class="text-xs text-green-600"><?php echo e($stats['active_clients'] ?? 0); ?> نشط</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">إجمالي المشاريع</p>
                    <p class="text-2xl font-bold text-secondary-900"><?php echo e($stats['total_projects'] ?? 0); ?></p>
                    <p class="text-xs text-green-600"><?php echo e($stats['active_projects'] ?? 0); ?> نشط</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-green-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">الإيرادات الشهرية</p>
                    <p class="text-2xl font-bold text-secondary-900"><?php echo e(number_format($stats['monthly_revenue'] ?? 0, 0)); ?> ريال</p>
                    <p class="text-xs text-secondary-500"><?php echo e(number_format($stats['yearly_revenue'] ?? 0, 0)); ?> ريال سنوياً</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-yellow-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-yellow-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>

        
        <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
            <div class="flex items-center justify-between">
                <div class="text-right">
                    <p class="text-sm font-medium text-secondary-600">أعضاء الفريق</p>
                    <p class="text-2xl font-bold text-secondary-900"><?php echo e($stats['team_members'] ?? 0); ?></p>
                    <p class="text-xs text-secondary-500">عضو نشط</p>
                </div>
                <div class="flex-shrink-0">
                    <div class="w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center">
                        <svg class="w-6 h-6 text-purple-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197m13.5-9a2.5 2.5 0 11-5 0 2.5 2.5 0 015 0z"></path>
                        </svg>
                    </div>
                </div>
            </div>
        </div>
    </div>

    
    <div class="bg-white rounded-xl shadow-sm border border-secondary-200 p-6">
        <h3 class="text-lg font-bold text-secondary-900 mb-4">معلومات التشخيص</h3>
        <div class="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
            <div>
                <strong>البيانات المرسلة:</strong>
                <pre class="mt-2 p-2 bg-secondary-50 rounded text-xs"><?php echo e(json_encode($stats ?? [], JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE)); ?></pre>
            </div>
            <div>
                <strong>معلومات إضافية:</strong>
                <ul class="mt-2 space-y-1">
                    <li>نوع اللوحة: <?php echo e($dashboardType ?? 'غير محدد'); ?></li>
                    <li>المستخدم: <?php echo e(auth()->user()->name ?? 'غير محدد'); ?></li>
                    <li>الأدوار: <?php echo e(auth()->user()->roles->pluck('name')->implode(', ') ?? 'لا توجد أدوار'); ?></li>
                </ul>
            </div>
        </div>
    </div>
</div>
<?php /**PATH C:\laragon\www\lial_Erp\resources\views/dashboard/founder-simple.blade.php ENDPATH**/ ?>