<?php

namespace App\Policies;

use App\Models\Payment;
use App\Models\User;
use Illuminate\Auth\Access\Response;

class PaymentPolicy
{
    /**
     * Determine whether the user can view any models.
     */
    public function viewAny(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can view the model.
     */
    public function view(User $user, Payment $payment): bool
    {
        // Founders, admins, and managers can view all payments
        if ($user->hasAnyRole(['founder', 'admin', 'manager'])) {
            return true;
        }

        // Employees can view payments they created or are assigned to
        if ($user->hasRole('employee')) {
            return $payment->created_by === $user->id;
        }

        // Clients can view their own payments
        if ($user->hasRole('client')) {
            return $user->email === $payment->client->email;
        }

        return false;
    }

    /**
     * Determine whether the user can create models.
     */
    public function create(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager', 'employee']);
    }

    /**
     * Determine whether the user can update the model.
     */
    public function update(User $user, Payment $payment): bool
    {
        // Founders and admins can update any payment
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can update payments in their scope
        if ($user->hasRole('manager')) {
            return true; // For now, allow all managers to update payments
        }

        // Employees can update payments they created (if not completed)
        if ($user->hasRole('employee')) {
            return $payment->created_by === $user->id && 
                   in_array($payment->status, ['pending', 'failed']);
        }

        return false;
    }

    /**
     * Determine whether the user can delete the model.
     */
    public function delete(User $user, Payment $payment): bool
    {
        // Only founders and admins can delete payments
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can delete pending payments
        if ($user->hasRole('manager') && $payment->status === 'pending') {
            return true;
        }

        return false;
    }

    /**
     * Determine whether the user can restore the model.
     */
    public function restore(User $user, Payment $payment): bool
    {
        return $user->hasAnyRole(['founder', 'admin']);
    }

    /**
     * Determine whether the user can permanently delete the model.
     */
    public function forceDelete(User $user, Payment $payment): bool
    {
        return $user->hasRole('founder');
    }

    /**
     * Determine whether the user can change payment status.
     */
    public function changeStatus(User $user, Payment $payment): bool
    {
        // Founders and admins can change any status
        if ($user->hasAnyRole(['founder', 'admin'])) {
            return true;
        }

        // Managers can change status for most payments
        if ($user->hasRole('manager')) {
            return true;
        }

        // Employees can only change status of their own payments
        if ($user->hasRole('employee')) {
            return $payment->created_by === $user->id;
        }

        return false;
    }

    /**
     * Determine whether the user can process refunds.
     */
    public function processRefund(User $user, Payment $payment): bool
    {
        // Only founders, admins, and managers can process refunds
        if ($user->hasAnyRole(['founder', 'admin', 'manager'])) {
            return $payment->status === 'completed';
        }

        return false;
    }

    /**
     * Determine whether the user can view payment gateway details.
     */
    public function viewGatewayDetails(User $user, Payment $payment): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can export payments data.
     */
    public function export(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can view payment analytics.
     */
    public function viewAnalytics(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin', 'manager']);
    }

    /**
     * Determine whether the user can manage payment methods.
     */
    public function managePaymentMethods(User $user): bool
    {
        return $user->hasAnyRole(['founder', 'admin']);
    }
}
