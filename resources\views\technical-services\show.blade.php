<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <div class="flex items-center space-x-4 space-x-reverse">
                <a href="{{ route('technical-services.index') }}" 
                   class="text-secondary-600 hover:text-secondary-900">
                    <x-icon name="arrow-right" class="w-5 h-5" />
                </a>
                <h2 class="font-semibold text-xl text-secondary-800 leading-tight">
                    {{ $technicalService->name_ar }}
                </h2>
                @if($technicalService->is_featured)
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-yellow-100 text-yellow-800">
                        <x-icon name="star" class="w-3 h-3 ml-1" />
                        مميزة
                    </span>
                @endif
            </div>
            <div class="flex items-center space-x-2 space-x-reverse">
                @can('update', $technicalService)
                    <a href="{{ route('technical-services.edit', $technicalService) }}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="pencil" class="w-4 h-4 ml-2" />
                        تعديل
                    </a>
                @endcan
                
                @can('delete', $technicalService)
                    <button type="button" 
                            onclick="confirmDelete()"
                            class="bg-red-600 hover:bg-red-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="trash" class="w-4 h-4 ml-2" />
                        حذف
                    </button>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
                <!-- Main Content -->
                <div class="lg:col-span-2 space-y-6">
                    <!-- Service Overview -->
                    <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                        <div class="px-6 py-4 border-b border-secondary-200">
                            <h3 class="text-lg font-medium text-secondary-900">نظرة عامة</h3>
                        </div>
                        <div class="p-6">
                            @if($technicalService->image)
                                <div class="mb-6">
                                    <img src="{{ asset('storage/' . $technicalService->image) }}" 
                                         alt="{{ $technicalService->name_ar }}" 
                                         class="w-full h-48 object-cover rounded-lg">
                                </div>
                            @endif
                            
                            <div class="space-y-4">
                                <div>
                                    <h4 class="text-sm font-medium text-secondary-500 mb-1">الوصف</h4>
                                    <p class="text-secondary-900">{{ $technicalService->description_ar }}</p>
                                    @if($technicalService->description_en)
                                        <p class="text-secondary-600 mt-2 text-sm">{{ $technicalService->description_en }}</p>
                                    @endif
                                </div>

                                @if($technicalService->features_ar)
                                    <div>
                                        <h4 class="text-sm font-medium text-secondary-500 mb-2">المميزات</h4>
                                        <ul class="space-y-1">
                                            @foreach(explode("\n", $technicalService->features_ar) as $feature)
                                                @if(trim($feature))
                                                    <li class="flex items-start">
                                                        <x-icon name="check" class="w-4 h-4 text-green-500 ml-2 mt-0.5 flex-shrink-0" />
                                                        <span class="text-secondary-900">{{ trim($feature) }}</span>
                                                    </li>
                                                @endif
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                @if($technicalService->requirements)
                                    <div>
                                        <h4 class="text-sm font-medium text-secondary-500 mb-2">المتطلبات</h4>
                                        <ul class="space-y-1">
                                            @foreach($technicalService->requirements as $requirement)
                                                <li class="flex items-start">
                                                    <x-icon name="exclamation-circle" class="w-4 h-4 text-yellow-500 ml-2 mt-0.5 flex-shrink-0" />
                                                    <span class="text-secondary-900">{{ $requirement }}</span>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                @if($technicalService->deliverables)
                                    <div>
                                        <h4 class="text-sm font-medium text-secondary-500 mb-2">المخرجات</h4>
                                        <ul class="space-y-1">
                                            @foreach($technicalService->deliverables as $deliverable)
                                                <li class="flex items-start">
                                                    <x-icon name="document-text" class="w-4 h-4 text-blue-500 ml-2 mt-0.5 flex-shrink-0" />
                                                    <span class="text-secondary-900">{{ $deliverable }}</span>
                                                </li>
                                            @endforeach
                                        </ul>
                                    </div>
                                @endif

                                @if($technicalService->technologies)
                                    <div>
                                        <h4 class="text-sm font-medium text-secondary-500 mb-2">التقنيات المستخدمة</h4>
                                        <div class="flex flex-wrap gap-2">
                                            @foreach($technicalService->technologies as $technology)
                                                <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                                                    {{ $technology }}
                                                </span>
                                            @endforeach
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                    </div>

                    <!-- Pricing Tiers -->
                    @if($technicalService->pricingTiers->count() > 0)
                        <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                            <div class="px-6 py-4 border-b border-secondary-200">
                                <h3 class="text-lg font-medium text-secondary-900">باقات التسعير</h3>
                            </div>
                            <div class="p-6">
                                <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                    @foreach($technicalService->pricingTiers as $tier)
                                        <div class="border border-secondary-200 rounded-lg p-4 {{ $tier->is_popular ? 'ring-2 ring-primary-500' : '' }}">
                                            @if($tier->is_popular)
                                                <div class="text-center mb-2">
                                                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-primary-100 text-primary-800">
                                                        الأكثر شعبية
                                                    </span>
                                                </div>
                                            @endif
                                            <h4 class="font-medium text-secondary-900 mb-2">{{ $tier->name_ar }}</h4>
                                            <p class="text-2xl font-bold text-primary-600 mb-2">{{ number_format($tier->price, 2) }} ر.س</p>
                                            <p class="text-sm text-secondary-600 mb-3">{{ $tier->description_ar }}</p>
                                            @if($tier->features)
                                                <ul class="space-y-1">
                                                    @foreach($tier->features as $feature)
                                                        <li class="flex items-start text-sm">
                                                            <x-icon name="check" class="w-3 h-3 text-green-500 ml-1 mt-0.5 flex-shrink-0" />
                                                            <span>{{ $feature }}</span>
                                                        </li>
                                                    @endforeach
                                                </ul>
                                            @endif
                                        </div>
                                    @endforeach
                                </div>
                            </div>
                        </div>
                    @endif
                </div>

                <!-- Sidebar -->
                <div class="space-y-6">
                    <!-- Service Details -->
                    <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                        <div class="px-6 py-4 border-b border-secondary-200">
                            <h3 class="text-lg font-medium text-secondary-900">تفاصيل الخدمة</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-secondary-500">الفئة</dt>
                                <dd class="mt-1 text-sm text-secondary-900">{{ $technicalService->category->name_ar ?? 'غير محدد' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-secondary-500">السعر الأساسي</dt>
                                <dd class="mt-1 text-lg font-semibold text-primary-600">{{ number_format($technicalService->base_price, 2) }} ر.س</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-secondary-500">مستوى التعقيد</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        @if($technicalService->complexity_level === 'simple') bg-green-100 text-green-800
                                        @elseif($technicalService->complexity_level === 'medium') bg-yellow-100 text-yellow-800
                                        @elseif($technicalService->complexity_level === 'complex') bg-orange-100 text-orange-800
                                        @else bg-red-100 text-red-800 @endif">
                                        @switch($technicalService->complexity_level)
                                            @case('simple') بسيط @break
                                            @case('medium') متوسط @break
                                            @case('complex') معقد @break
                                            @case('enterprise') مؤسسي @break
                                        @endswitch
                                    </span>
                                </dd>
                            </div>
                            @if($technicalService->estimated_hours)
                                <div>
                                    <dt class="text-sm font-medium text-secondary-500">الساعات المقدرة</dt>
                                    <dd class="mt-1 text-sm text-secondary-900">{{ $technicalService->estimated_hours }} ساعة</dd>
                                </div>
                            @endif
                            @if($technicalService->estimated_days)
                                <div>
                                    <dt class="text-sm font-medium text-secondary-500">الأيام المقدرة</dt>
                                    <dd class="mt-1 text-sm text-secondary-900">{{ $technicalService->estimated_days }} يوم</dd>
                                </div>
                            @endif
                            <div>
                                <dt class="text-sm font-medium text-secondary-500">الحالة</dt>
                                <dd class="mt-1">
                                    <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                        @if($technicalService->status === 'active') bg-green-100 text-green-800
                                        @elseif($technicalService->status === 'draft') bg-gray-100 text-gray-800
                                        @elseif($technicalService->status === 'inactive') bg-red-100 text-red-800
                                        @else bg-yellow-100 text-yellow-800 @endif">
                                        @switch($technicalService->status)
                                            @case('active') نشط @break
                                            @case('draft') مسودة @break
                                            @case('inactive') غير نشط @break
                                            @case('archived') مؤرشف @break
                                        @endswitch
                                    </span>
                                </dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-secondary-500">قابلة للتخصيص</dt>
                                <dd class="mt-1 text-sm text-secondary-900">
                                    {{ $technicalService->is_customizable ? 'نعم' : 'لا' }}
                                </dd>
                            </div>
                        </div>
                    </div>

                    <!-- Meta Information -->
                    <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                        <div class="px-6 py-4 border-b border-secondary-200">
                            <h3 class="text-lg font-medium text-secondary-900">معلومات إضافية</h3>
                        </div>
                        <div class="p-6 space-y-4">
                            <div>
                                <dt class="text-sm font-medium text-secondary-500">تم الإنشاء بواسطة</dt>
                                <dd class="mt-1 text-sm text-secondary-900">{{ $technicalService->creator->name ?? 'غير محدد' }}</dd>
                            </div>
                            <div>
                                <dt class="text-sm font-medium text-secondary-500">تاريخ الإنشاء</dt>
                                <dd class="mt-1 text-sm text-secondary-900">{{ $technicalService->created_at->format('Y/m/d H:i') }}</dd>
                            </div>
                            @if($technicalService->updated_at != $technicalService->created_at)
                                <div>
                                    <dt class="text-sm font-medium text-secondary-500">آخر تحديث</dt>
                                    <dd class="mt-1 text-sm text-secondary-900">{{ $technicalService->updated_at->format('Y/m/d H:i') }}</dd>
                                </div>
                                @if($technicalService->updater)
                                    <div>
                                        <dt class="text-sm font-medium text-secondary-500">تم التحديث بواسطة</dt>
                                        <dd class="mt-1 text-sm text-secondary-900">{{ $technicalService->updater->name }}</dd>
                                    </div>
                                @endif
                            @endif
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function confirmDelete() {
            if (confirm('هل أنت متأكد من حذف هذه الخدمة؟ لا يمكن التراجع عن هذا الإجراء.')) {
                // Create and submit delete form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = '{{ route("technical-services.destroy", $technicalService) }}';
                
                const methodInput = document.createElement('input');
                methodInput.type = 'hidden';
                methodInput.name = '_method';
                methodInput.value = 'DELETE';
                
                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = '_token';
                tokenInput.value = '{{ csrf_token() }}';
                
                form.appendChild(methodInput);
                form.appendChild(tokenInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
    @endpush
</x-app-layout>
