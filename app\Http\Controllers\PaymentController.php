<?php

namespace App\Http\Controllers;

use App\Http\Requests\StorePaymentRequest;
use App\Http\Requests\UpdatePaymentRequest;
use App\Models\Payment;
use App\Models\Invoice;
use App\Models\Client;
use Illuminate\Http\Request;
use Illuminate\View\View;
use Illuminate\Http\RedirectResponse;
use Illuminate\Support\Facades\Auth;

class PaymentController extends Controller
{
    /**
     * Display a listing of payments.
     */
    public function index(Request $request): View
    {
        $this->authorize('viewAny', Payment::class);

        return view('payments.index');
    }

    /**
     * Show the form for creating a new payment.
     */
    public function create(Request $request): View
    {
        $this->authorize('create', Payment::class);

        $invoice = null;
        if ($request->has('invoice_id')) {
            $invoice = Invoice::findOrFail($request->invoice_id);
            $this->authorize('view', $invoice);
        }

        $clients = Client::active()->orderBy('name_ar')->get();
        $invoices = Invoice::where('payment_status', '!=', 'paid')
                          ->with('client')
                          ->orderBy('created_at', 'desc')
                          ->get();

        return view('payments.create', compact('invoice', 'clients', 'invoices'));
    }

    /**
     * Store a newly created payment in storage.
     */
    public function store(StorePaymentRequest $request): RedirectResponse
    {
        $this->authorize('create', Payment::class);

        $validated = $request->validated();
        $validated['created_by'] = Auth::id();

        $payment = Payment::create($validated);

        // Update invoice payment status if payment is linked to an invoice
        if ($payment->invoice_id) {
            $payment->invoice->updatePaymentStatus();
        }

        return redirect()
            ->route('payments.show', $payment)
            ->with('success', __('تم تسجيل الدفعة بنجاح'));
    }

    /**
     * Display the specified payment.
     */
    public function show(Payment $payment): View
    {
        $this->authorize('view', $payment);

        $payment->load(['client', 'invoice', 'creator', 'updater']);

        return view('payments.show', compact('payment'));
    }

    /**
     * Show the form for editing the specified payment.
     */
    public function edit(Payment $payment): View
    {
        $this->authorize('update', $payment);

        $clients = Client::active()->orderBy('name_ar')->get();
        $invoices = Invoice::where('payment_status', '!=', 'paid')
                          ->orWhere('id', $payment->invoice_id)
                          ->with('client')
                          ->orderBy('created_at', 'desc')
                          ->get();

        return view('payments.edit', compact('payment', 'clients', 'invoices'));
    }

    /**
     * Update the specified payment in storage.
     */
    public function update(UpdatePaymentRequest $request, Payment $payment): RedirectResponse
    {
        $this->authorize('update', $payment);

        $validated = $request->validated();
        $validated['updated_by'] = Auth::id();

        $oldInvoiceId = $payment->invoice_id;
        $payment->update($validated);

        // Update payment status for old and new invoices
        if ($oldInvoiceId && $oldInvoiceId != $payment->invoice_id) {
            Invoice::find($oldInvoiceId)?->updatePaymentStatus();
        }

        if ($payment->invoice_id) {
            $payment->invoice->updatePaymentStatus();
        }

        return redirect()
            ->route('payments.show', $payment)
            ->with('success', __('تم تحديث بيانات الدفعة بنجاح'));
    }

    /**
     * Remove the specified payment from storage.
     */
    public function destroy(Payment $payment): RedirectResponse
    {
        $this->authorize('delete', $payment);

        $invoiceId = $payment->invoice_id;
        $paymentNumber = $payment->payment_number;

        $payment->delete();

        // Update invoice payment status after deletion
        if ($invoiceId) {
            Invoice::find($invoiceId)?->updatePaymentStatus();
        }

        return redirect()
            ->route('payments.index')
            ->with('success', __('تم حذف الدفعة :number بنجاح', ['number' => $paymentNumber]));
    }

    /**
     * Update the status of a payment.
     */
    public function updateStatus(Request $request, Payment $payment): RedirectResponse
    {
        $this->authorize('update', $payment);

        $request->validate([
            'status' => 'required|in:pending,completed,failed,cancelled,refunded'
        ]);

        $payment->update([
            'status' => $request->status,
            'updated_by' => Auth::id()
        ]);

        // Update invoice payment status if payment is linked to an invoice
        if ($payment->invoice_id) {
            $payment->invoice->updatePaymentStatus();
        }

        return back()->with('success', __('تم تحديث حالة الدفعة بنجاح'));
    }

    /**
     * Get payments data for API/AJAX requests.
     */
    public function getData(Request $request)
    {
        $this->authorize('viewAny', Payment::class);

        $query = Payment::with(['client', 'invoice', 'creator'])
            ->when($request->search, function ($query, $search) {
                $query->where(function ($q) use ($search) {
                    $q->where('payment_number', 'like', "%{$search}%")
                      ->orWhere('reference_number', 'like', "%{$search}%")
                      ->orWhere('transaction_id', 'like', "%{$search}%")
                      ->orWhereHas('client', function ($clientQuery) use ($search) {
                          $clientQuery->where('name_ar', 'like', "%{$search}%")
                                     ->orWhere('name_en', 'like', "%{$search}%");
                      });
                });
            })
            ->when($request->client_id, function ($query, $clientId) {
                $query->where('client_id', $clientId);
            })
            ->when($request->invoice_id, function ($query, $invoiceId) {
                $query->where('invoice_id', $invoiceId);
            })
            ->when($request->status, function ($query, $status) {
                $query->where('status', $status);
            })
            ->when($request->payment_method, function ($query, $method) {
                $query->where('payment_method', $method);
            })
            ->when($request->date_from, function ($query, $dateFrom) {
                $query->whereDate('payment_date', '>=', $dateFrom);
            })
            ->when($request->date_to, function ($query, $dateTo) {
                $query->whereDate('payment_date', '<=', $dateTo);
            });

        // Apply sorting
        $sortField = $request->get('sort', 'payment_date');
        $sortDirection = $request->get('direction', 'desc');

        $allowedSortFields = [
            'payment_number', 'amount', 'payment_date', 'payment_method',
            'status', 'created_at'
        ];

        if (in_array($sortField, $allowedSortFields)) {
            $query->orderBy($sortField, $sortDirection);
        }

        $payments = $query->paginate($request->get('per_page', 15));

        return response()->json($payments);
    }
}
