<?php

namespace App\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StoreTechnicalServiceRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true; // Authorization is handled in the controller
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules(): array
    {
        return [
            'name_ar' => 'required|string|max:255',
            'name_en' => 'nullable|string|max:255',
            'description_ar' => 'required|string',
            'description_en' => 'nullable|string',
            'features_ar' => 'nullable|array',
            'features_en' => 'nullable|array',
            'category_id' => 'required|exists:technical_service_categories,id',
            'base_price' => 'required|numeric|min:0',
            'estimated_hours' => 'nullable|integer|min:1',
            'estimated_days' => 'nullable|integer|min:1',
            'complexity_level' => 'required|in:simple,medium,complex,enterprise',
            'status' => 'required|in:active,inactive,draft,archived',
            'requirements' => 'nullable|array',
            'deliverables' => 'nullable|array',
            'technologies' => 'nullable|array',
            'image' => 'nullable|image|mimes:jpeg,png,jpg,gif|max:2048',
            'gallery' => 'nullable|array',
            'gallery.*' => 'image|mimes:jpeg,png,jpg,gif|max:2048',
            'is_featured' => 'boolean',
            'is_customizable' => 'boolean',
            'sort_order' => 'nullable|integer|min:0',
        ];
    }

    /**
     * Get custom attributes for validator errors.
     */
    public function attributes(): array
    {
        return [
            'name_ar' => 'اسم الخدمة بالعربية',
            'name_en' => 'اسم الخدمة بالإنجليزية',
            'description_ar' => 'وصف الخدمة بالعربية',
            'description_en' => 'وصف الخدمة بالإنجليزية',
            'features_ar' => 'مميزات الخدمة بالعربية',
            'features_en' => 'مميزات الخدمة بالإنجليزية',
            'category_id' => 'فئة الخدمة',
            'base_price' => 'السعر الأساسي',
            'estimated_hours' => 'الساعات المقدرة',
            'estimated_days' => 'الأيام المقدرة',
            'complexity_level' => 'مستوى التعقيد',
            'status' => 'حالة الخدمة',
            'requirements' => 'المتطلبات',
            'deliverables' => 'المخرجات',
            'technologies' => 'التقنيات المستخدمة',
            'image' => 'صورة الخدمة',
            'gallery' => 'معرض الصور',
            'is_featured' => 'خدمة مميزة',
            'is_customizable' => 'قابلة للتخصيص',
            'sort_order' => 'ترتيب العرض',
        ];
    }

    /**
     * Get custom messages for validator errors.
     */
    public function messages(): array
    {
        return [
            'name_ar.required' => 'اسم الخدمة بالعربية مطلوب',
            'description_ar.required' => 'وصف الخدمة بالعربية مطلوب',
            'category_id.required' => 'فئة الخدمة مطلوبة',
            'category_id.exists' => 'فئة الخدمة المحددة غير صحيحة',
            'base_price.required' => 'السعر الأساسي مطلوب',
            'base_price.numeric' => 'السعر الأساسي يجب أن يكون رقماً',
            'base_price.min' => 'السعر الأساسي يجب أن يكون أكبر من أو يساوي صفر',
            'complexity_level.required' => 'مستوى التعقيد مطلوب',
            'complexity_level.in' => 'مستوى التعقيد المحدد غير صحيح',
            'status.required' => 'حالة الخدمة مطلوبة',
            'status.in' => 'حالة الخدمة المحددة غير صحيحة',
            'image.image' => 'الملف المرفوع يجب أن يكون صورة',
            'image.mimes' => 'صيغة الصورة يجب أن تكون: jpeg, png, jpg, gif',
            'image.max' => 'حجم الصورة يجب أن يكون أقل من 2 ميجابايت',
            'gallery.*.image' => 'جميع ملفات المعرض يجب أن تكون صور',
            'gallery.*.mimes' => 'صيغة صور المعرض يجب أن تكون: jpeg, png, jpg, gif',
            'gallery.*.max' => 'حجم كل صورة في المعرض يجب أن يكون أقل من 2 ميجابايت',
        ];
    }
}
