<x-app-layout>
    <x-slot name="header">
        <div class="flex items-center justify-between">
            <h2 class="font-semibold text-xl text-secondary-800 leading-tight">
                {{ __('إدارة الخدمات التقنية') }}
            </h2>
            <div class="flex items-center space-x-2 space-x-reverse">
                @can('create', App\Models\TechnicalService::class)
                    <a href="{{ route('technical-services.create') }}" 
                       class="bg-primary-600 hover:bg-primary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="plus" class="w-4 h-4 ml-2" />
                        إضافة خدمة جديدة
                    </a>
                @endcan
                
                @can('export', App\Models\TechnicalService::class)
                    <button type="button" 
                            class="bg-secondary-600 hover:bg-secondary-700 text-white px-4 py-2 rounded-lg text-sm font-medium transition-colors duration-200 flex items-center">
                        <x-icon name="arrow-down-tray" class="w-4 h-4 ml-2" />
                        تصدير
                    </button>
                @endcan
            </div>
        </div>
    </x-slot>

    <div class="py-6">
        <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
            <!-- Statistics Cards -->
            <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-blue-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="cog" class="w-5 h-5 text-blue-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">إجمالي الخدمات</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\TechnicalService::count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-green-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="check-circle" class="w-5 h-5 text-green-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">الخدمات النشطة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\TechnicalService::where('status', 'active')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-yellow-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="star" class="w-5 h-5 text-yellow-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">الخدمات المميزة</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\TechnicalService::where('is_featured', true)->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="bg-white overflow-hidden shadow-sm rounded-lg border border-secondary-200">
                    <div class="p-6">
                        <div class="flex items-center">
                            <div class="flex-shrink-0">
                                <div class="w-8 h-8 bg-purple-100 rounded-lg flex items-center justify-center">
                                    <x-icon name="document-text" class="w-5 h-5 text-purple-600" />
                                </div>
                            </div>
                            <div class="mr-4 flex-1">
                                <div class="text-sm font-medium text-secondary-500">مسودات</div>
                                <div class="text-2xl font-bold text-secondary-900">
                                    {{ \App\Models\TechnicalService::where('status', 'draft')->count() }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Services Table -->
            <div class="bg-white shadow-sm rounded-lg border border-secondary-200">
                <div class="px-6 py-4 border-b border-secondary-200">
                    <div class="flex items-center justify-between">
                        <h3 class="text-lg font-medium text-secondary-900">قائمة الخدمات التقنية</h3>
                        <div class="flex items-center space-x-2 space-x-reverse">
                            <!-- Search -->
                            <div class="relative">
                                <input type="text" 
                                       placeholder="البحث في الخدمات..." 
                                       class="w-64 pl-10 pr-4 py-2 border border-secondary-300 rounded-lg focus:ring-primary-500 focus:border-primary-500 text-sm">
                                <div class="absolute inset-y-0 right-0 pr-3 flex items-center pointer-events-none">
                                    <x-icon name="magnifying-glass" class="h-4 w-4 text-secondary-400" />
                                </div>
                            </div>
                            
                            <!-- Filter -->
                            <select class="border border-secondary-300 rounded-lg px-3 py-2 text-sm focus:ring-primary-500 focus:border-primary-500">
                                <option value="">جميع الحالات</option>
                                <option value="active">نشطة</option>
                                <option value="draft">مسودة</option>
                                <option value="inactive">غير نشطة</option>
                                <option value="archived">مؤرشفة</option>
                            </select>
                        </div>
                    </div>
                </div>

                <div class="overflow-x-auto">
                    <table class="min-w-full divide-y divide-secondary-200">
                        <thead class="bg-secondary-50">
                            <tr>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                    الخدمة
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                    الفئة
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                    السعر الأساسي
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                    مستوى التعقيد
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                    الحالة
                                </th>
                                <th scope="col" class="px-6 py-3 text-right text-xs font-medium text-secondary-500 uppercase tracking-wider">
                                    الإجراءات
                                </th>
                            </tr>
                        </thead>
                        <tbody class="bg-white divide-y divide-secondary-200">
                            @forelse(\App\Models\TechnicalService::with('category')->latest()->paginate(15) as $service)
                                <tr class="hover:bg-secondary-50">
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <div class="flex items-center">
                                            @if($service->is_featured)
                                                <x-icon name="star" class="w-4 h-4 text-yellow-500 ml-2" />
                                            @endif
                                            <div>
                                                <div class="text-sm font-medium text-secondary-900">
                                                    {{ $service->name_ar }}
                                                </div>
                                                @if($service->name_en)
                                                    <div class="text-sm text-secondary-500">
                                                        {{ $service->name_en }}
                                                    </div>
                                                @endif
                                            </div>
                                        </div>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm text-secondary-900">
                                            {{ $service->category->name_ar ?? 'غير محدد' }}
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="text-sm font-medium text-secondary-900">
                                            {{ number_format($service->base_price, 2) }} ر.س
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                            @if($service->complexity_level === 'simple') bg-green-100 text-green-800
                                            @elseif($service->complexity_level === 'medium') bg-yellow-100 text-yellow-800
                                            @elseif($service->complexity_level === 'complex') bg-orange-100 text-orange-800
                                            @else bg-red-100 text-red-800 @endif">
                                            @switch($service->complexity_level)
                                                @case('simple') بسيط @break
                                                @case('medium') متوسط @break
                                                @case('complex') معقد @break
                                                @case('enterprise') مؤسسي @break
                                            @endswitch
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap">
                                        <span class="inline-flex px-2 py-1 text-xs font-semibold rounded-full
                                            @if($service->status === 'active') bg-green-100 text-green-800
                                            @elseif($service->status === 'draft') bg-gray-100 text-gray-800
                                            @elseif($service->status === 'inactive') bg-red-100 text-red-800
                                            @else bg-yellow-100 text-yellow-800 @endif">
                                            @switch($service->status)
                                                @case('active') نشط @break
                                                @case('draft') مسودة @break
                                                @case('inactive') غير نشط @break
                                                @case('archived') مؤرشف @break
                                            @endswitch
                                        </span>
                                    </td>
                                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                        <div class="flex items-center space-x-2 space-x-reverse">
                                            @can('view', $service)
                                                <a href="{{ route('technical-services.show', $service) }}" 
                                                   class="text-primary-600 hover:text-primary-900">
                                                    <x-icon name="eye" class="w-4 h-4" />
                                                </a>
                                            @endcan
                                            
                                            @can('update', $service)
                                                <a href="{{ route('technical-services.edit', $service) }}" 
                                                   class="text-secondary-600 hover:text-secondary-900">
                                                    <x-icon name="pencil" class="w-4 h-4" />
                                                </a>
                                            @endcan
                                            
                                            @can('delete', $service)
                                                <button type="button" 
                                                        onclick="confirmDelete('{{ $service->id }}')"
                                                        class="text-red-600 hover:text-red-900">
                                                    <x-icon name="trash" class="w-4 h-4" />
                                                </button>
                                            @endcan
                                        </div>
                                    </td>
                                </tr>
                            @empty
                                <tr>
                                    <td colspan="6" class="px-6 py-12 text-center">
                                        <div class="text-secondary-500">
                                            <x-icon name="cog" class="w-12 h-12 mx-auto mb-4 text-secondary-300" />
                                            <p class="text-lg font-medium">لا توجد خدمات تقنية</p>
                                            <p class="mt-1">ابدأ بإضافة خدمة تقنية جديدة</p>
                                        </div>
                                    </td>
                                </tr>
                            @endforelse
                        </tbody>
                    </table>
                </div>

                <!-- Pagination -->
                @if(\App\Models\TechnicalService::count() > 15)
                    <div class="px-6 py-4 border-t border-secondary-200">
                        {{ \App\Models\TechnicalService::latest()->paginate(15)->links() }}
                    </div>
                @endif
            </div>
        </div>
    </div>

    @push('scripts')
    <script>
        function confirmDelete(serviceId) {
            if (confirm('هل أنت متأكد من حذف هذه الخدمة؟')) {
                // Create and submit delete form
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `/technical-services/${serviceId}`;
                
                const methodInput = document.createElement('input');
                methodInput.type = 'hidden';
                methodInput.name = '_method';
                methodInput.value = 'DELETE';
                
                const tokenInput = document.createElement('input');
                tokenInput.type = 'hidden';
                tokenInput.name = '_token';
                tokenInput.value = '{{ csrf_token() }}';
                
                form.appendChild(methodInput);
                form.appendChild(tokenInput);
                document.body.appendChild(form);
                form.submit();
            }
        }
    </script>
    @endpush
</x-app-layout>
