{{-- Sidebar Navigation Component --}}
<div class="flex flex-col h-full">
    {{-- Logo and Company Info --}}
    <div class="flex items-center justify-center px-6 py-6 border-b border-secondary-200 bg-gradient-to-r from-primary-50 to-primary-100">
        <x-application-logo
            size="default"
            variant="compact"
            :clickable="true"
            :showText="true"
            class="w-full" />
    </div>

    {{-- User Info --}}
    <div class="px-6 py-4 border-b border-secondary-200 bg-secondary-50">
        <div class="flex items-center">
            <div class="flex-shrink-0">
                <div class="h-10 w-10 rounded-full bg-primary-600 flex items-center justify-center">
                    <span class="text-sm font-medium text-white">
                        {{ substr(auth()->user()->name, 0, 2) }}
                    </span>
                </div>
            </div>
            <div class="mr-3 text-right">
                <div class="text-sm font-medium text-secondary-900">{{ auth()->user()->name }}</div>
                <div class="text-xs text-secondary-500">
                    @if(auth()->user()->hasRole('founder'))
                        {{ __('app.roles.founder') }}
                    @elseif(auth()->user()->hasRole('admin'))
                        {{ __('app.roles.admin') }}
                    @elseif(auth()->user()->hasRole('manager'))
                        {{ __('app.roles.manager') }}
                    @elseif(auth()->user()->hasRole('employee'))
                        {{ __('app.roles.employee') }}
                    @elseif(auth()->user()->hasRole('client'))
                        {{ __('app.roles.client') }}
                    @endif
                </div>
            </div>
        </div>
    </div>

    {{-- Navigation Menu --}}
    <nav class="flex-1 px-4 py-6 space-y-2 overflow-y-auto">
        {{-- Dashboard --}}
        <x-nav-item href="{{ route('dashboard') }}" :active="request()->routeIs('dashboard')" icon="home">
            {{ __('app.navigation.dashboard') }}
        </x-nav-item>

        {{-- Technical Services --}}
        @can('viewAny', App\Models\TechnicalService::class)
            <x-nav-item href="{{ route('technical-services.index') }}" :active="request()->routeIs('technical-services.*')" icon="cog">
                {{ __('app.navigation.technical_services') }}
            </x-nav-item>
        @endcan

        {{-- Client Management --}}
        @can('viewAny', App\Models\Client::class)
            <x-nav-item href="{{ route('clients.index') }}" :active="request()->routeIs('clients.*')" icon="users">
                {{ __('app.navigation.clients') }}
            </x-nav-item>
        @endcan

        {{-- Financial Management --}}
        @can('view_finance')
            <x-nav-group label="{{ __('app.navigation.finance') }}" icon="currency-dollar" :active="request()->routeIs('finance.*')">
                <x-nav-sub-item href="#" :active="request()->routeIs('invoices.*')">
                    {{ __('app.finance.invoices') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('payments.*')">
                    {{ __('app.finance.payments') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('expenses.*')">
                    {{ __('app.finance.expenses') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('reports.*')">
                    {{ __('app.finance.reports') }}
                </x-nav-sub-item>
            </x-nav-group>
        @endcan

        {{-- Project Management --}}
        @can('viewAny', App\Models\Project::class)
            <x-nav-group label="{{ __('app.navigation.projects') }}" icon="clipboard-list" :active="request()->routeIs('projects.*')">
                <x-nav-sub-item href="{{ route('projects.index') }}" :active="request()->routeIs('projects.index')">
                    جميع المشاريع
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('projects.tasks.*')">
                    {{ __('app.projects.tasks') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('projects.files.*')">
                    {{ __('app.projects.files') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('projects.milestones.*')">
                    {{ __('app.projects.milestones') }}
                </x-nav-sub-item>
            </x-nav-group>
        @endcan

        {{-- Financial Management --}}
        @can('viewAny', App\Models\Invoice::class)
            <x-nav-group label="{{ __('app.navigation.finances') }}" icon="banknotes" :active="request()->routeIs('invoices.*', 'payments.*')">
                <x-nav-sub-item href="{{ route('invoices.index') }}" :active="request()->routeIs('invoices.*')">
                    {{ __('app.finances.invoices') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('payments.index') }}" :active="request()->routeIs('payments.*')">
                    {{ __('app.finances.payments') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('expenses.*')">
                    {{ __('app.finances.expenses') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="#" :active="request()->routeIs('reports.*')">
                    {{ __('app.finances.reports') }}
                </x-nav-sub-item>
            </x-nav-group>
        @endcan

        {{-- Team Management --}}
        @can('view_team')
            <x-nav-group label="{{ __('app.navigation.team') }}" icon="user-group" :active="request()->routeIs('team.*')">
                <x-nav-sub-item href="{{ route('team.departments') }}" :active="request()->routeIs('team.departments')">
                    الأقسام
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('team.members') }}" :active="request()->routeIs('team.members')">
                    أعضاء الفريق
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('team.attendance') }}" :active="request()->routeIs('team.attendance')">
                    {{ __('app.team.attendance') }}
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('team.performance') }}" :active="request()->routeIs('team.performance')">
                    {{ __('app.team.performance') }}
                </x-nav-sub-item>
            </x-nav-group>
        @endcan

        {{-- Inventory Management --}}
        @can('view_inventory')
            <x-nav-group label="{{ __('app.navigation.inventory') }}" icon="cube" :active="request()->routeIs('inventory.*')">
                <x-nav-sub-item href="{{ route('inventory.products') }}" :active="request()->routeIs('inventory.products')">
                    المنتجات والخدمات
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('inventory.categories') }}" :active="request()->routeIs('inventory.categories')">
                    فئات المنتجات
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('inventory.movements') }}" :active="request()->routeIs('inventory.movements')">
                    حركات المخزون
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('inventory.alerts') }}" :active="request()->routeIs('inventory.alerts')">
                    تنبيهات المخزون
                </x-nav-sub-item>
            </x-nav-group>
        @endcan

        {{-- Expense Management --}}
        @can('view_expenses')
            <x-nav-group label="{{ __('app.navigation.expenses') }}" icon="banknotes" :active="request()->routeIs('expenses.*')">
                <x-nav-sub-item href="{{ route('expenses.index') }}" :active="request()->routeIs('expenses.index')">
                    المصروفات
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('expenses.categories') }}" :active="request()->routeIs('expenses.categories')">
                    فئات المصروفات
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('expenses.budgets') }}" :active="request()->routeIs('expenses.budgets')">
                    الميزانيات
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('expenses.reports') }}" :active="request()->routeIs('expenses.reports')">
                    تقارير المصروفات
                </x-nav-sub-item>
            </x-nav-group>
        @endcan

        {{-- Reports & Analytics --}}
        @can('view_analytics')
            <x-nav-group label="{{ __('app.navigation.reports') }}" icon="chart-bar" :active="request()->routeIs('reports.*')">
                <x-nav-sub-item href="{{ route('reports.dashboard') }}" :active="request()->routeIs('reports.dashboard')">
                    لوحة التقارير
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('reports.financial') }}" :active="request()->routeIs('reports.financial')">
                    التقارير المالية
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('reports.inventory') }}" :active="request()->routeIs('reports.inventory')">
                    تقارير المخزون
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('reports.team') }}" :active="request()->routeIs('reports.team')">
                    تقارير الفريق
                </x-nav-sub-item>
                <x-nav-sub-item href="{{ route('reports.analytics') }}" :active="request()->routeIs('reports.analytics')">
                    التحليلات المتقدمة
                </x-nav-sub-item>
            </x-nav-group>
        @endcan

        {{-- Settings (Founder/Admin only) --}}
        @can('view_system_settings')
            <x-nav-item href="#" :active="request()->routeIs('settings.*')" icon="cog-6-tooth">
                {{ __('app.navigation.settings') }}
            </x-nav-item>
        @endcan
    </nav>

    {{-- Footer --}}
    <div class="px-6 py-4 border-t border-secondary-200">
        <div class="text-center">
            <p class="text-xs text-secondary-500">&copy; {{ date('Y') }} {{ __('app.company.name') }}</p>
            <p class="text-xs text-secondary-400 mt-1">الإصدار 1.0.0</p>
        </div>
    </div>
</div>
